import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { apiRequest } from '@/utils/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const user = ref(null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // 登录
  const login = async (credentials) => {
    isLoading.value = true
    try {
      const response = await apiRequest('/api/login', {
        method: 'POST',
        data: credentials
      })

      if (response.success) {
        token.value = response.token
        user.value = response.user
        
        // 保存到本地存储
        localStorage.setItem('token', response.token)
        localStorage.setItem('user', JSON.stringify(response.user))
        
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败，请重试' 
      }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = () => {
    token.value = ''
    user.value = null
    
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    
    // 可以调用后端登出接口
    // await apiRequest('/api/logout', { method: 'POST' })
  }

  // 检查认证状态
  const checkAuth = async () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (!savedToken || !savedUser) {
      logout()
      return false
    }

    try {
      // 验证token是否有效
      const response = await apiRequest('/api/validate-token', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${savedToken}`
        }
      })

      if (response.valid) {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
        return true
      } else {
        logout()
        return false
      }
    } catch (error) {
      console.error('Token验证失败:', error)
      logout()
      return false
    }
  }

  // 更新用户信息
  const updateUser = (userData) => {
    user.value = { ...user.value, ...userData }
    localStorage.setItem('user', JSON.stringify(user.value))
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const response = await apiRequest('/api/refresh-token', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token.value}`
        }
      })

      if (response.success) {
        token.value = response.token
        localStorage.setItem('token', response.token)
        return true
      }
      return false
    } catch (error) {
      console.error('刷新token失败:', error)
      logout()
      return false
    }
  }

  return {
    // 状态
    token: readonly(token),
    user: readonly(user),
    isLoading: readonly(isLoading),
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    
    // 方法
    login,
    logout,
    checkAuth,
    updateUser,
    refreshToken
  }
})
