# 创收管理系统 - Vue版

基于Vue 3 + Element Plus的现代化创收管理系统，从原有的HTML/JavaScript版本重构而来。

## ✨ 特性

- 🚀 **现代化技术栈**: Vue 3 + Vite + Element Plus
- 📱 **响应式设计**: 完美适配桌面和移动设备
- 🎨 **主题切换**: 支持明暗主题和跟随系统
- 📊 **数据可视化**: Chart.js驱动的丰富图表
- 🔐 **权限管理**: 基于JWT的身份认证
- 📁 **文件上传**: 支持图片上传和WebP转换
- 🔍 **智能搜索**: 实时搜索和高级筛选
- 📈 **数据分析**: 多维度数据统计和趋势分析

## 🛠️ 技术栈

### 前端
- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **图表**: Chart.js
- **样式**: SCSS
- **图标**: Font Awesome

### 后端 (复用原系统)
- **运行时**: Node.js
- **框架**: Express.js
- **数据库**: MySQL
- **认证**: JWT
- **文件上传**: Multer

## 📦 安装

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 克隆项目
```bash
git clone <repository-url>
cd revenue-management-vue
```

### 安装依赖
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

## 🚀 开发

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 📁 项目结构

```
src/
├── components/          # 公共组件
│   ├── RecordDialog.vue    # 记录编辑对话框
│   └── ChartsDialog.vue    # 图表分析对话框
├── stores/             # Pinia状态管理
│   ├── auth.js            # 认证状态
│   ├── theme.js           # 主题状态
│   └── revenue.js         # 创收数据状态
├── utils/              # 工具函数
│   ├── api.js             # API请求封装
│   └── format.js          # 格式化工具
├── views/              # 页面组件
│   ├── Login.vue          # 登录页
│   ├── Dashboard.vue      # 仪表板布局
│   ├── RevenueManagement.vue # 创收管理主页
│   └── NotFound.vue       # 404页面
├── styles/             # 样式文件
│   └── main.scss          # 主样式文件
├── router/             # 路由配置
│   └── index.js
├── App.vue             # 根组件
└── main.js             # 入口文件
```

## 🔧 配置

### 环境变量
复制 `.env.example` 到 `.env` 并修改配置：

```bash
# API配置
VITE_API_BASE_URL=http://localhost:3001

# 开发配置
VITE_DEV_PORT=3000
```

### 后端API
确保后端服务运行在 `http://localhost:3001`，或修改 `VITE_API_BASE_URL` 配置。

## 📋 功能模块

### 1. 用户认证
- 登录/登出
- JWT Token管理
- 权限控制

### 2. 创收管理
- 创收记录的增删改查
- 图片上传和预览
- 状态管理
- 批量操作

### 3. 数据筛选
- 实时搜索
- 店铺筛选
- 状态筛选
- 日期范围筛选

### 4. 数据分析
- 创收趋势图
- 店铺分布图
- 月度对比图
- 状态分布图

### 5. 数据导出
- Excel导出
- 图表导出

## 🎨 主题系统

支持三种主题模式：
- **浅色模式**: 默认浅色主题
- **深色模式**: 深色主题
- **跟随系统**: 自动跟随系统主题

## 📱 响应式设计

- **桌面端**: 完整功能，表格视图
- **平板端**: 适配中等屏幕
- **移动端**: 卡片视图，触摸优化

## 🔍 搜索功能

- **实时搜索**: 300ms防抖
- **多字段搜索**: 订单号、店铺名称、产品信息、备注
- **拼音搜索**: 支持中文拼音搜索

## 📊 图表分析

使用Chart.js提供丰富的数据可视化：
- 线性图：创收趋势
- 饼图：店铺分布
- 柱状图：月度对比
- 环形图：状态分布

## 🚀 性能优化

- **代码分割**: 路由级别的懒加载
- **组件懒加载**: 按需加载组件
- **图片优化**: WebP格式转换
- **缓存策略**: 合理的缓存配置
- **打包优化**: Vite构建优化

## 🔒 安全特性

- JWT Token认证
- 请求拦截器
- XSS防护
- CSRF防护
- 文件上传安全检查

## 🐛 调试

### 开发环境
- Vue DevTools
- 控制台日志
- 网络请求监控

### 生产环境
- 错误边界
- 全局错误处理
- 性能监控

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 🎉 初始版本发布
- ✨ 完整的创收管理功能
- 🎨 现代化UI设计
- 📱 响应式布局
- 📊 数据可视化

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 📞 联系方式

如有问题，请联系开发团队。
