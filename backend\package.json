{"name": "revenue-management-backend", "version": "1.0.0", "description": "创收管理系统后端服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["revenue", "management", "mysql", "express", "api"], "author": "Your Name", "license": "MIT"}