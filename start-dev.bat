@echo off
echo ========================================
echo 启动创收管理系统开发环境
echo ========================================

echo.
echo 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

echo.
echo 检查MySQL服务...
sc query mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: MySQL服务未运行，请确保MySQL已启动
    echo 如果使用XAMPP，请启动XAMPP控制面板并启动MySQL
    pause
)

echo.
echo 安装后端依赖...
cd backend
if not exist node_modules (
    echo 正在安装后端依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: 后端依赖安装失败
        pause
        exit /b 1
    )
)

echo.
echo 初始化数据库...
node scripts/init-database.js
if %errorlevel% neq 0 (
    echo 警告: 数据库初始化失败，请检查MySQL连接配置
    echo 请确保MySQL服务已启动，用户名密码为root/root
    pause
)

echo.
echo 启动后端服务...
start "后端服务" cmd /k "npm start"

echo.
echo 等待后端服务启动...
timeout /t 3 /nobreak >nul

cd ..

echo.
echo 安装前端依赖...
if not exist node_modules (
    echo 正在安装前端依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: 前端依赖安装失败
        pause
        exit /b 1
    )
)

echo.
echo 启动前端服务...
start "前端服务" cmd /k "npm run dev"

echo.
echo ========================================
echo 服务启动完成！
echo.
echo 前端地址: http://localhost:5173
echo 后端地址: http://localhost:3001
echo.
echo 默认管理员账号:
echo 用户名: admin
echo 密码: 123456
echo.
echo 按任意键关闭此窗口...
echo ========================================
pause >nul
