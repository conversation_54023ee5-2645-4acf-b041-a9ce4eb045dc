<template>
  <div id="app" :class="{ 'dark': isDark }">
    <router-view />
    
    <!-- 全局通知容器 -->
    <div id="notification-container" class="notification-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, provide } from 'vue'
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()
const isDark = computed(() => themeStore.isDark)

// 提供全局主题状态
provide('isDark', isDark)

onMounted(() => {
  // 初始化主题
  themeStore.initTheme()
  
  // 监听系统主题变化
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  mediaQuery.addEventListener('change', (e) => {
    if (themeStore.theme === 'system') {
      themeStore.setTheme('system')
    }
  })
})
</script>

<style lang="scss">
// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  height: 100%;
  transition: all 0.3s ease;
}

// 深色主题
.dark {
  color-scheme: dark;
  
  body {
    background-color: #1a1a1a;
    color: #ffffff;
  }
}

// 通知容器
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  pointer-events: none;
  
  .notification {
    pointer-events: auto;
    margin-bottom: 10px;
    min-width: 300px;
    max-width: 400px;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    animation: slideInRight 0.3s ease-out;
    
    &.success {
      background: rgba(34, 197, 94, 0.9);
      color: white;
      border-left: 4px solid #16a34a;
    }
    
    &.error {
      background: rgba(239, 68, 68, 0.9);
      color: white;
      border-left: 4px solid #dc2626;
    }
    
    &.warning {
      background: rgba(245, 158, 11, 0.9);
      color: white;
      border-left: 4px solid #d97706;
    }
    
    &.info {
      background: rgba(59, 130, 246, 0.9);
      color: white;
      border-left: 4px solid #2563eb;
    }
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  
  &:hover {
    background: #a8a8a8;
  }
}

.dark {
  ::-webkit-scrollbar-track {
    background: #2d2d2d;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #555;
    
    &:hover {
      background: #777;
    }
  }
}
</style>
