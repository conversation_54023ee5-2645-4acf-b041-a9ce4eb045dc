# 创收管理系统 - 部署说明

## 📦 生产环境部署

### 1. 构建生产版本

```bash
# 安装依赖
npm install

# 构建生产版本
npm run build
```

构建完成后，会在 `dist` 目录生成静态文件。

### 2. 服务器部署

#### 方法一：使用Nginx
1. 将 `dist` 目录内容上传到服务器
2. 配置Nginx：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理（如果需要）
    location /api/ {
        proxy_pass http://your-backend-server:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 方法二：使用Apache
1. 将 `dist` 目录内容上传到服务器
2. 在根目录创建 `.htaccess` 文件：

```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    RewriteRule ^index\.html$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.html [L]
</IfModule>

# 静态资源缓存
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
</IfModule>
```

#### 方法三：使用Node.js服务器
创建简单的Express服务器：

```javascript
const express = require('express');
const path = require('path');
const app = express();

// 静态文件服务
app.use(express.static(path.join(__dirname, 'dist')));

// 处理Vue Router
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'dist/index.html'));
});

const port = process.env.PORT || 3000;
app.listen(port, () => {
    console.log(`Server running on port ${port}`);
});
```

### 3. 环境配置

#### 生产环境变量
创建 `.env.production` 文件：

```bash
# API配置
VITE_API_BASE_URL=https://your-api-domain.com

# 构建配置
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_DROP_CONSOLE=true

# 功能配置
VITE_ENABLE_DEBUG=false
VITE_ENABLE_MOCK=false
```

#### SSL证书配置
推荐使用HTTPS：

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 其他配置...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 🐳 Docker部署

### 1. 创建Dockerfile

```dockerfile
# 构建阶段
FROM node:18-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 2. 创建nginx.conf

```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
        }

        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

### 3. 构建和运行

```bash
# 构建镜像
docker build -t revenue-management .

# 运行容器
docker run -d -p 80:80 revenue-management
```

### 4. Docker Compose

```yaml
version: '3.8'
services:
  frontend:
    build: .
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  # 如果有后端服务
  backend:
    image: your-backend-image
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=your-database-url
    restart: unless-stopped
```

## ☁️ 云平台部署

### Vercel部署
1. 连接GitHub仓库
2. 配置构建命令：`npm run build`
3. 配置输出目录：`dist`
4. 设置环境变量

### Netlify部署
1. 连接Git仓库
2. 构建命令：`npm run build`
3. 发布目录：`dist`
4. 配置重定向规则：

```
/*    /index.html   200
```

### AWS S3 + CloudFront
1. 构建项目：`npm run build`
2. 上传到S3存储桶
3. 配置CloudFront分发
4. 设置自定义错误页面指向index.html

## 🔧 性能优化

### 1. 构建优化
```javascript
// vite.config.js
export default {
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus'],
          charts: ['chart.js']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
}
```

### 2. 缓存策略
- 静态资源设置长期缓存
- HTML文件设置短期缓存
- 使用CDN加速

### 3. 压缩优化
- 启用Gzip压缩
- 使用Brotli压缩（如果支持）
- 图片压缩和WebP格式

## 📊 监控和维护

### 1. 错误监控
集成错误监控服务（如Sentry）：

```javascript
import * as Sentry from "@sentry/vue";

Sentry.init({
  app,
  dsn: "YOUR_DSN_HERE",
  environment: import.meta.env.MODE,
});
```

### 2. 性能监控
- 使用Google Analytics
- 配置性能监控
- 设置用户行为追踪

### 3. 日志管理
- 配置访问日志
- 错误日志收集
- 性能指标监控

## 🔒 安全配置

### 1. 安全头设置
```nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

### 2. 防火墙配置
- 限制访问IP
- 配置DDoS防护
- 设置访问频率限制

## 📋 部署检查清单

- [ ] 构建生产版本无错误
- [ ] 环境变量配置正确
- [ ] 静态资源路径正确
- [ ] 路由重定向配置
- [ ] SSL证书配置
- [ ] 缓存策略设置
- [ ] 安全头配置
- [ ] 错误监控集成
- [ ] 性能监控配置
- [ ] 备份策略制定

---

**部署成功后，记得测试所有功能是否正常工作！** 🚀
