#!/bin/bash

echo "========================================"
echo "启动创收管理系统开发环境"
echo "========================================"

echo ""
echo "检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请先安装Node.js"
    exit 1
fi

echo ""
echo "检查MySQL服务..."
if ! pgrep -x "mysqld" > /dev/null; then
    echo "警告: MySQL服务未运行，请确保MySQL已启动"
    echo "Ubuntu/Debian: sudo systemctl start mysql"
    echo "CentOS/RHEL: sudo systemctl start mysqld"
    echo "macOS: brew services start mysql"
    read -p "按Enter继续..."
fi

echo ""
echo "安装后端依赖..."
cd backend
if [ ! -d "node_modules" ]; then
    echo "正在安装后端依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "错误: 后端依赖安装失败"
        exit 1
    fi
fi

echo ""
echo "初始化数据库..."
node scripts/init-database.js
if [ $? -ne 0 ]; then
    echo "警告: 数据库初始化失败，请检查MySQL连接配置"
    echo "请确保MySQL服务已启动，用户名密码为root/root"
    read -p "按Enter继续..."
fi

echo ""
echo "启动后端服务..."
npm start &
BACKEND_PID=$!

echo ""
echo "等待后端服务启动..."
sleep 3

cd ..

echo ""
echo "安装前端依赖..."
if [ ! -d "node_modules" ]; then
    echo "正在安装前端依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "错误: 前端依赖安装失败"
        exit 1
    fi
fi

echo ""
echo "启动前端服务..."
npm run dev &
FRONTEND_PID=$!

echo ""
echo "========================================"
echo "服务启动完成！"
echo ""
echo "前端地址: http://localhost:5173"
echo "后端地址: http://localhost:3001"
echo ""
echo "默认管理员账号:"
echo "用户名: admin"
echo "密码: 123456"
echo ""
echo "按Ctrl+C停止所有服务"
echo "========================================"

# 等待用户中断
trap "echo ''; echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT

# 保持脚本运行
wait
