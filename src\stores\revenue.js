import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { apiRequest } from '@/utils/api'
import { useAuthStore } from './auth'

export const useRevenueStore = defineStore('revenue', () => {
  // 状态
  const records = ref([])
  const statistics = ref({
    totalRevenue: 0,
    totalOrders: 0,
    totalShops: 0,
    avgOrderValue: 0
  })
  const shops = ref([])
  const isLoading = ref(false)
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const filters = ref({
    search: '',
    shopName: '',
    startDate: '',
    endDate: '',
    status: ''
  })

  // 计算属性
  const filteredRecords = computed(() => {
    let result = records.value
    
    if (filters.value.search) {
      const searchTerm = filters.value.search.toLowerCase()
      result = result.filter(record => 
        record.shop_name?.toLowerCase().includes(searchTerm) ||
        record.order_number?.toLowerCase().includes(searchTerm) ||
        record.product_info?.toLowerCase().includes(searchTerm) ||
        record.revenue_notes?.toLowerCase().includes(searchTerm)
      )
    }
    
    if (filters.value.shopName) {
      result = result.filter(record => record.shop_name === filters.value.shopName)
    }
    
    if (filters.value.status) {
      result = result.filter(record => record.status === filters.value.status)
    }
    
    return result
  })

  // 获取创收记录列表
  const fetchRecords = async (params = {}) => {
    isLoading.value = true
    try {
      const queryParams = {
        page: pagination.value.current,
        limit: pagination.value.pageSize,
        ...filters.value,
        ...params
      }

      const response = await apiRequest('/api/revenue', {
        method: 'GET',
        params: queryParams
      })

      if (response.success) {
        records.value = response.data.records || []
        if (response.data.pagination) {
          pagination.value = { ...pagination.value, ...response.data.pagination }
        }
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('获取创收记录失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '获取数据失败' 
      }
    } finally {
      isLoading.value = false
    }
  }

  // 获取统计数据
  const fetchStatistics = async (filterParams = {}) => {
    try {
      const response = await apiRequest('/api/revenue/statistics/overview', {
        method: 'GET',
        params: filterParams
      })

      if (response.success) {
        statistics.value = response.data.total || {}
        return { success: true, data: response.data }
      }
      return { success: false, message: response.message }
    } catch (error) {
      console.error('获取统计数据失败:', error)
      return { success: false, message: '获取统计数据失败' }
    }
  }

  // 获取店铺列表
  const fetchShops = async () => {
    try {
      const response = await apiRequest('/api/shops/active', {
        method: 'GET'
      })

      if (response.success) {
        shops.value = response.data || []
        return { success: true }
      }
      return { success: false, message: response.message }
    } catch (error) {
      console.error('获取店铺列表失败:', error)
      return { success: false, message: '获取店铺列表失败' }
    }
  }

  // 创建创收记录
  const createRecord = async (recordData, imageFile = null) => {
    try {
      let imageUrl = null

      // 如果有图片文件，先上传图片
      if (imageFile) {
        const uploadResult = await uploadImage(imageFile)
        if (!uploadResult.success) {
          return uploadResult
        }
        imageUrl = uploadResult.data.url
      }

      // 创建记录数据
      const dataToSend = {
        ...recordData,
        imageUrl
      }

      const response = await apiRequest('/api/revenue', {
        method: 'POST',
        data: dataToSend
      })

      if (response.success) {
        await fetchRecords() // 刷新列表
        await fetchStatistics() // 刷新统计
        return { success: true, message: response.message }
      }
      return { success: false, message: response.message }
    } catch (error) {
      console.error('创建创收记录失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || '创建记录失败'
      }
    }
  }

  // 更新创收记录
  const updateRecord = async (id, recordData, imageFile = null) => {
    try {
      let dataToSend = { ...recordData }

      // 如果有新的图片文件，先上传图片
      if (imageFile) {
        const uploadResult = await uploadImage(imageFile)
        if (!uploadResult.success) {
          return uploadResult
        }
        dataToSend.imageUrl = uploadResult.data.url
      }

      const response = await apiRequest(`/api/revenue/${id}`, {
        method: 'PUT',
        data: dataToSend
      })

      if (response.success) {
        await fetchRecords()
        await fetchStatistics()
        return { success: true, message: response.message }
      }
      return { success: false, message: response.message }
    } catch (error) {
      console.error('更新创收记录失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || '更新记录失败' 
      }
    }
  }

  // 上传图片
  const uploadImage = async (imageFile) => {
    try {
      const formData = new FormData()
      formData.append('image', imageFile)

      const response = await apiRequest('/api/revenue/upload', {
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      if (response.success) {
        return { success: true, data: response.data }
      }
      return { success: false, message: response.message }
    } catch (error) {
      console.error('图片上传失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || '图片上传失败'
      }
    }
  }

  // 删除创收记录
  const deleteRecord = async (id) => {
    try {
      const response = await apiRequest(`/api/revenue/${id}`, {
        method: 'DELETE'
      })

      if (response.success) {
        await fetchRecords()
        await fetchStatistics()
        return { success: true, message: response.message }
      }
      return { success: false, message: response.message }
    } catch (error) {
      console.error('删除创收记录失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || '删除记录失败'
      }
    }
  }

  // 更新记录状态
  const updateRecordStatus = async (id, status) => {
    try {
      const response = await apiRequest(`/api/revenue/${id}`, {
        method: 'PUT',
        data: { status }
      })

      if (response.success) {
        // 更新本地记录
        const record = records.value.find(r => r.id === id)
        if (record) {
          record.status = status
        }
        return { success: true, message: response.message }
      }
      return { success: false, message: response.message }
    } catch (error) {
      console.error('更新状态失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || '更新状态失败'
      }
    }
  }

  // 批量删除记录
  const batchDeleteRecords = async (ids) => {
    try {
      const response = await apiRequest('/api/revenue/batch', {
        method: 'DELETE',
        data: { ids }
      })

      if (response.success) {
        await fetchRecords()
        await fetchStatistics()
        return { success: true, message: response.message }
      }
      return { success: false, message: response.message }
    } catch (error) {
      console.error('批量删除失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || '批量删除失败'
      }
    }
  }

  // 设置筛选条件
  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
    pagination.value.current = 1 // 重置到第一页
  }

  // 设置分页
  const setPagination = (newPagination) => {
    pagination.value = { ...pagination.value, ...newPagination }
  }

  // 重置筛选条件
  const resetFilters = () => {
    filters.value = {
      search: '',
      shopName: '',
      startDate: '',
      endDate: '',
      status: ''
    }
    pagination.value.current = 1
  }

  return {
    // 状态
    records: readonly(records),
    statistics: readonly(statistics),
    shops: readonly(shops),
    isLoading: readonly(isLoading),
    pagination: readonly(pagination),
    filters: readonly(filters),
    
    // 计算属性
    filteredRecords,
    
    // 方法
    fetchRecords,
    fetchStatistics,
    fetchShops,
    createRecord,
    updateRecord,
    uploadImage,
    deleteRecord,
    updateRecordStatus,
    batchDeleteRecords,
    setFilters,
    setPagination,
    resetFilters
  }
})
