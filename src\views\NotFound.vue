<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <div class="error-animation">
        <div class="error-number">404</div>
        <div class="error-icon">
          <i class="fas fa-search"></i>
        </div>
      </div>
      
      <div class="error-message">
        <h1>页面未找到</h1>
        <p>抱歉，您访问的页面不存在或已被移除。</p>
      </div>
      
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          <i class="fas fa-home"></i>
          返回首页
        </el-button>
        <el-button @click="goBack">
          <i class="fas fa-arrow-left"></i>
          返回上页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/dashboard')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.not-found-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.not-found-content {
  text-align: center;
  color: white;
  max-width: 500px;
}

.error-animation {
  position: relative;
  margin-bottom: 40px;
  
  .error-number {
    font-size: 120px;
    font-weight: 900;
    color: rgba(255, 255, 255, 0.1);
    line-height: 1;
    margin-bottom: 20px;
    animation: float 3s ease-in-out infinite;
  }
  
  .error-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    
    i {
      font-size: 60px;
      color: white;
      animation: pulse 2s ease-in-out infinite;
    }
  }
}

.error-message {
  margin-bottom: 40px;
  
  h1 {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 16px;
    color: white;
  }
  
  p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin: 0;
  }
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
  
  .el-button {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 8px;
    
    i {
      margin-right: 8px;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-animation {
    .error-number {
      font-size: 80px;
    }
    
    .error-icon i {
      font-size: 40px;
    }
  }
  
  .error-message h1 {
    font-size: 24px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
    
    .el-button {
      width: 200px;
    }
  }
}
</style>
