// 主样式文件

// 变量定义
:root {
  // 主色调
  --primary-color: #409eff;
  --primary-light: #79bbff;
  --primary-dark: #337ecc;
  
  // 成功色
  --success-color: #67c23a;
  --success-light: #95d475;
  --success-dark: #529b2e;
  
  // 警告色
  --warning-color: #e6a23c;
  --warning-light: #ebb563;
  --warning-dark: #b88230;
  
  // 危险色
  --danger-color: #f56c6c;
  --danger-light: #f78989;
  --danger-dark: #c45656;
  
  // 信息色
  --info-color: #909399;
  --info-light: #a6a9ad;
  --info-dark: #73767a;
  
  // 文本颜色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  // 边框颜色
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;
  
  // 背景颜色
  --bg-color: #ffffff;
  --bg-color-page: #f2f3f5;
  --bg-color-overlay: rgba(255, 255, 255, 0.9);
  
  // 阴影
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  // 圆角
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-round: 20px;
  --border-radius-circle: 100%;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 字体大小
  --font-size-extra-small: 12px;
  --font-size-small: 13px;
  --font-size-base: 14px;
  --font-size-medium: 16px;
  --font-size-large: 18px;
  --font-size-extra-large: 20px;
  
  // 动画
  --transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  --transition-md-fade: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

// 深色主题变量
.dark {
  --text-primary: #e5eaf3;
  --text-regular: #cfd3dc;
  --text-secondary: #a3a6ad;
  --text-placeholder: #8d9095;
  
  --border-base: #4c4d4f;
  --border-light: #414243;
  --border-lighter: #363637;
  --border-extra-light: #2b2b2c;
  
  --bg-color: #1d1e1f;
  --bg-color-page: #0a0a0a;
  --bg-color-overlay: rgba(29, 30, 31, 0.9);
  
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.48), 0 0 6px rgba(0, 0, 0, 0.16);
  --box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.48), 0 0 6px rgba(0, 0, 0, 0.48);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.4);
}

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  color: var(--text-primary);
  background-color: var(--bg-color-page);
  transition: var(--transition-base);
}

// 链接样式
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-base);
  
  &:hover {
    color: var(--primary-light);
  }
  
  &:active {
    color: var(--primary-dark);
  }
}

// 按钮样式增强
.el-button {
  transition: var(--transition-base);
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 卡片样式增强
.el-card {
  border: 1px solid var(--border-lighter);
  box-shadow: var(--box-shadow-light);
  transition: var(--transition-base);
  
  &:hover {
    box-shadow: var(--box-shadow-dark);
    transform: translateY(-2px);
  }
}

// 表格样式增强
.el-table {
  .el-table__header {
    background-color: var(--bg-color);
  }
  
  .el-table__row {
    transition: var(--transition-base);
    
    &:hover {
      background-color: var(--border-extra-light);
    }
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }
.flex-column { display: flex; flex-direction: column; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.m-0 { margin: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: var(--transition-fade);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: var(--transition-md-fade);
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

// 高亮样式
.highlight {
  background-color: #fff3cd;
  color: #856404;
  padding: 2px 4px;
  border-radius: 2px;
  font-weight: 500;
}

.dark .highlight {
  background-color: #664d03;
  color: #fff3cd;
}

// 加载状态
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-color-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

// 响应式工具类
@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--border-extra-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-base);
  border-radius: 4px;
  transition: var(--transition-base);
  
  &:hover {
    background: var(--text-secondary);
  }
}

// 打印样式
@media print {
  .no-print {
    display: none !important;
  }
  
  .el-button,
  .el-pagination {
    display: none !important;
  }
}
