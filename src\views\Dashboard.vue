<template>
  <div class="dashboard-container">
    <!-- 顶部导航 -->
    <div class="dashboard-header">
      <div class="header-left">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          <span class="logo-text">创收管理系统</span>
        </div>
      </div>
      
      <div class="header-right">
        <!-- 主题切换 -->
        <el-tooltip :content="themeStore.getThemeName" placement="bottom">
          <el-button
            circle
            :icon="themeStore.getThemeIcon"
            @click="themeStore.toggleTheme"
          />
        </el-tooltip>
        
        <!-- 用户菜单 -->
        <el-dropdown @command="handleUserCommand">
          <div class="user-info">
            <el-avatar :size="32">
              <i class="fas fa-user"></i>
            </el-avatar>
            <span class="username">{{ authStore.user?.username }}</span>
            <i class="fas fa-chevron-down"></i>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <i class="fas fa-user-circle"></i>
                个人资料
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <i class="fas fa-cog"></i>
                系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="dashboard-main">
      <!-- 侧边导航 -->
      <div class="dashboard-sidebar">
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          @select="handleMenuSelect"
        >
          <el-menu-item index="/dashboard">
            <i class="fas fa-tachometer-alt"></i>
            <span>仪表板</span>
          </el-menu-item>
          <el-menu-item index="/revenue">
            <i class="fas fa-dollar-sign"></i>
            <span>创收管理</span>
          </el-menu-item>
        </el-menu>
      </div>
      
      <!-- 内容区域 -->
      <div class="dashboard-content">
        <router-view v-if="$route.path !== '/dashboard'" />
        <div v-else class="welcome-content">
          <div class="welcome-card">
            <div class="welcome-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h2>欢迎使用创收管理系统</h2>
            <p>Vue版本 - 现代化管理平台</p>
            <el-button type="primary" @click="$router.push('/revenue')">
              开始管理创收
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const themeStore = useThemeStore()

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 处理菜单选择
const handleMenuSelect = (index) => {
  if (index !== route.path) {
    router.push(index)
  }
}

// 处理用户菜单命令
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '退出确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        authStore.logout()
        ElMessage.success('已退出登录')
        router.push('/login')
      } catch {
        // 用户取消退出
      }
      break
  }
}

onMounted(() => {
  // 检查认证状态
  if (!authStore.isAuthenticated) {
    router.push('/login')
  }
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color-page);
}

.dashboard-header {
  height: 60px;
  background-color: var(--bg-color);
  border-bottom: 1px solid var(--border-lighter);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: var(--box-shadow-light);
  z-index: 100;
  
  .header-left {
    display: flex;
    align-items: center;
    
    .logo {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-color);
      
      i {
        font-size: 24px;
      }
      
      .logo-text {
        color: var(--text-primary);
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: var(--transition-base);
      
      &:hover {
        background-color: var(--border-extra-light);
      }
      
      .username {
        font-size: 14px;
        color: var(--text-primary);
        font-weight: 500;
      }
      
      i.fa-chevron-down {
        font-size: 12px;
        color: var(--text-secondary);
        transition: var(--transition-base);
      }
    }
  }
}

.dashboard-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.dashboard-sidebar {
  width: 200px;
  background-color: var(--bg-color);
  border-right: 1px solid var(--border-lighter);
  overflow-y: auto;
  
  .sidebar-menu {
    border: none;
    
    .el-menu-item {
      height: 48px;
      line-height: 48px;
      margin: 4px 8px;
      border-radius: 8px;
      
      i {
        margin-right: 8px;
        width: 16px;
        text-align: center;
      }
      
      &:hover {
        background-color: var(--primary-color);
        color: white;
      }
      
      &.is-active {
        background-color: var(--primary-color);
        color: white;
        
        &::before {
          display: none;
        }
      }
    }
  }
}

.dashboard-content {
  flex: 1;
  overflow: auto;
  padding: 24px;

  .welcome-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;

    .welcome-card {
      text-align: center;
      padding: 48px;
      background: var(--bg-color);
      border-radius: 16px;
      box-shadow: var(--box-shadow-light);
      max-width: 400px;

      .welcome-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        border-radius: 50%;
        margin-bottom: 24px;

        i {
          font-size: 36px;
          color: white;
        }
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 12px;
      }

      p {
        color: var(--text-secondary);
        margin-bottom: 32px;
        font-size: 16px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-sidebar {
    width: 60px;
    
    .sidebar-menu {
      .el-menu-item {
        span {
          display: none;
        }
        
        i {
          margin-right: 0;
        }
      }
    }
  }
  
  .dashboard-header {
    padding: 0 16px;
    
    .header-left .logo .logo-text {
      display: none;
    }
    
    .header-right .user-info .username {
      display: none;
    }
  }
  
  .dashboard-content {
    padding: 16px;
  }
}
</style>
