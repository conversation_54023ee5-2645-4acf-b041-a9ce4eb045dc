import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 处理FormData
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type']
    }
    
    // 开发环境下打印请求信息
    if (import.meta.env.DEV) {
      console.log('🚀 API Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        data: config.data,
        params: config.params
      })
    }
    
    return config
  },
  (error) => {
    console.error('请求配置错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 开发环境下打印响应信息
    if (import.meta.env.DEV) {
      console.log('✅ API Response:', {
        status: response.status,
        url: response.config.url,
        data: response.data
      })
    }
    
    return response.data
  },
  (error) => {
    console.error('API请求失败:', error)
    
    // 处理不同类型的错误
    let errorMessage = '请求失败，请重试'
    
    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          errorMessage = '登录已过期，请重新登录'
          // 清除本地存储的认证信息
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          // 重定向到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
        case 403:
          errorMessage = '没有权限执行此操作'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 422:
          errorMessage = data?.message || '数据验证失败'
          break
        case 429:
          errorMessage = '请求过于频繁，请稍后再试'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = data?.message || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }
    
    // 显示错误消息
    ElMessage.error(errorMessage)
    
    return Promise.reject(error)
  }
)

// 通用API请求函数
export const apiRequest = async (url, options = {}) => {
  try {
    const {
      method = 'GET',
      data,
      params,
      headers = {},
      ...restOptions
    } = options

    const config = {
      method,
      url,
      ...restOptions
    }

    if (data) {
      config.data = data
    }

    if (params) {
      config.params = params
    }

    if (Object.keys(headers).length > 0) {
      config.headers = { ...config.headers, ...headers }
    }

    const response = await api(config)
    return response
  } catch (error) {
    throw error
  }
}

// 文件上传专用函数
export const uploadFile = async (url, file, onProgress = null) => {
  try {
    const formData = new FormData()
    formData.append('file', file)

    const config = {
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }

    if (onProgress) {
      config.onUploadProgress = (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress(percentCompleted)
      }
    }

    const response = await api(config)
    return response
  } catch (error) {
    throw error
  }
}

// 下载文件函数
export const downloadFile = async (url, filename = null) => {
  try {
    const response = await api({
      method: 'GET',
      url,
      responseType: 'blob'
    })

    // 创建下载链接
    const blob = new Blob([response])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)

    return { success: true }
  } catch (error) {
    console.error('文件下载失败:', error)
    throw error
  }
}

export default api
