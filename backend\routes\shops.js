const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { Shop } = require('../models');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// 获取所有店铺
router.get('/', authenticateToken, [
  query('status').optional().isIn(['active', 'inactive']).withMessage('状态值无效'),
  query('search').optional().isString().withMessage('搜索关键词必须是字符串')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { status, search } = req.query;
    const whereClause = {};

    if (status) {
      whereClause.status = status;
    }

    if (search) {
      whereClause.name = {
        [Shop.sequelize.Sequelize.Op.like]: `%${search}%`
      };
    }

    const shops = await Shop.findAll({
      where: whereClause,
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: shops
    });
  } catch (error) {
    console.error('获取店铺列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取店铺列表失败'
    });
  }
});

// 获取活跃店铺（用于下拉选择）
router.get('/active', authenticateToken, async (req, res) => {
  try {
    const shops = await Shop.getActiveShops();
    
    res.json({
      success: true,
      data: shops
    });
  } catch (error) {
    console.error('获取活跃店铺错误:', error);
    res.status(500).json({
      success: false,
      message: '获取活跃店铺失败'
    });
  }
});

// 获取单个店铺
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const shop = await Shop.findByPk(id);
    if (!shop) {
      return res.status(404).json({
        success: false,
        message: '店铺不存在'
      });
    }

    res.json({
      success: true,
      data: shop
    });
  } catch (error) {
    console.error('获取店铺详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取店铺详情失败'
    });
  }
});

// 创建店铺（需要管理员权限）
router.post('/', authenticateToken, requireAdmin, [
  body('name').notEmpty().withMessage('店铺名称不能为空').isLength({ max: 100 }).withMessage('店铺名称不能超过100个字符'),
  body('description').optional().isString().withMessage('描述必须是字符串'),
  body('status').optional().isIn(['active', 'inactive']).withMessage('状态值无效')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const shop = await Shop.create(req.body);

    res.status(201).json({
      success: true,
      message: '店铺创建成功',
      data: shop
    });
  } catch (error) {
    console.error('创建店铺错误:', error);
    
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        success: false,
        message: '店铺名称已存在'
      });
    }
    
    res.status(500).json({
      success: false,
      message: '创建店铺失败'
    });
  }
});

// 更新店铺（需要管理员权限）
router.put('/:id', authenticateToken, requireAdmin, [
  body('name').optional().notEmpty().withMessage('店铺名称不能为空').isLength({ max: 100 }).withMessage('店铺名称不能超过100个字符'),
  body('description').optional().isString().withMessage('描述必须是字符串'),
  body('status').optional().isIn(['active', 'inactive']).withMessage('状态值无效')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    
    const shop = await Shop.findByPk(id);
    if (!shop) {
      return res.status(404).json({
        success: false,
        message: '店铺不存在'
      });
    }

    await shop.update(req.body);

    res.json({
      success: true,
      message: '店铺更新成功',
      data: shop
    });
  } catch (error) {
    console.error('更新店铺错误:', error);
    
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        success: false,
        message: '店铺名称已存在'
      });
    }
    
    res.status(500).json({
      success: false,
      message: '更新店铺失败'
    });
  }
});

// 删除店铺（需要管理员权限）
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const shop = await Shop.findByPk(id);
    if (!shop) {
      return res.status(404).json({
        success: false,
        message: '店铺不存在'
      });
    }

    await shop.destroy();

    res.json({
      success: true,
      message: '店铺删除成功'
    });
  } catch (error) {
    console.error('删除店铺错误:', error);
    res.status(500).json({
      success: false,
      message: '删除店铺失败'
    });
  }
});

// 批量更新店铺状态（需要管理员权限）
router.patch('/batch/status', authenticateToken, requireAdmin, [
  body('ids').isArray({ min: 1 }).withMessage('必须提供至少一个店铺ID'),
  body('ids.*').isInt({ min: 1 }).withMessage('店铺ID必须是正整数'),
  body('status').isIn(['active', 'inactive']).withMessage('状态值无效')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { ids, status } = req.body;
    
    const [updatedCount] = await Shop.update(
      { status },
      { where: { id: ids } }
    );

    res.json({
      success: true,
      message: `成功更新 ${updatedCount} 个店铺的状态`
    });
  } catch (error) {
    console.error('批量更新店铺状态错误:', error);
    res.status(500).json({
      success: false,
      message: '批量更新店铺状态失败'
    });
  }
});

module.exports = router;
