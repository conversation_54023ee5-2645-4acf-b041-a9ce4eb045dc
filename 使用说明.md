# 创收管理系统 - 使用说明

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）
1. 双击 `start.bat` 文件
2. 等待依赖安装和服务器启动
3. 浏览器会自动打开 http://localhost:3000

### 方法二：手动启动
1. 打开命令行工具（CMD 或 PowerShell）
2. 进入项目目录
3. 运行以下命令：
```bash
npm install    # 安装依赖（首次运行）
npm run dev    # 启动开发服务器
```

## 📋 系统要求

- **Node.js**: 版本 16.0.0 或更高
- **浏览器**: Chrome、Firefox、Safari、Edge（现代浏览器）
- **操作系统**: Windows、macOS、Linux

## 🔧 功能说明

### 1. 登录系统
- 默认用户名：admin
- 默认密码：123456
- 支持记住登录状态

### 2. 创收管理
- **新增记录**: 点击"新增记录"按钮
- **编辑记录**: 点击表格中的编辑按钮
- **删除记录**: 点击表格中的删除按钮
- **状态管理**: 直接在表格中修改状态

### 3. 数据筛选
- **搜索**: 在搜索框中输入关键词
- **店铺筛选**: 选择特定店铺
- **状态筛选**: 按订单状态筛选
- **日期筛选**: 选择日期范围

### 4. 数据分析
- 点击"数据分析"按钮查看图表
- 包含创收趋势、店铺分布等多种图表

### 5. 数据导出
- 点击"导出数据"按钮
- 支持Excel格式导出

## 🎨 界面说明

### 主题切换
- 点击右上角的主题切换按钮
- 支持浅色、深色、跟随系统三种模式

### 响应式设计
- **桌面端**: 完整表格视图
- **移动端**: 卡片式布局，更适合触摸操作

## 📱 移动端使用

在手机或平板上访问系统时：
1. 界面会自动适配小屏幕
2. 表格会变为卡片式布局
3. 支持触摸滑动和点击操作

## 🔍 搜索技巧

- **订单号搜索**: 输入完整或部分订单号
- **店铺搜索**: 输入店铺名称
- **产品搜索**: 输入产品信息关键词
- **组合搜索**: 可以同时使用多个筛选条件

## 📊 图表说明

### 创收趋势图
- 显示最近30天的创收变化
- 可以看出业务增长趋势

### 店铺分布图
- 饼图显示各店铺的创收占比
- 帮助识别主要创收来源

### 月度对比图
- 柱状图显示各月创收对比
- 便于分析季节性变化

### 状态分布图
- 显示不同订单状态的分布
- 帮助了解订单处理情况

## ⚠️ 注意事项

### 数据安全
- 定期备份重要数据
- 不要在公共网络上使用
- 及时退出登录

### 性能优化
- 建议使用Chrome或Firefox浏览器
- 定期清理浏览器缓存
- 避免同时打开过多标签页

### 常见问题

**Q: 页面加载很慢怎么办？**
A: 检查网络连接，清理浏览器缓存，或尝试刷新页面

**Q: 图片上传失败怎么办？**
A: 确保图片格式为JPG、PNG或GIF，大小不超过5MB

**Q: 数据没有保存怎么办？**
A: 检查网络连接，确保所有必填字段都已填写

**Q: 忘记密码怎么办？**
A: 联系系统管理员重置密码

## 📞 技术支持

如遇到技术问题，请：
1. 检查浏览器控制台是否有错误信息
2. 尝试刷新页面或重启浏览器
3. 联系技术支持团队

## 🔄 更新说明

### v1.0.0 (当前版本)
- ✨ 全新Vue.js版本
- 🎨 现代化界面设计
- 📱 完整响应式支持
- 📊 丰富的数据可视化
- 🔐 安全的用户认证

---

**祝您使用愉快！** 🎉
