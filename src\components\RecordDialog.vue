<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑创收记录' : '新增创收记录'"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit.prevent="handleSubmit"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="店铺名称" prop="shop_name">
            <el-select
              v-model="formData.shop_name"
              placeholder="选择或输入店铺名称"
              filterable
              allow-create
              default-first-option
              style="width: 100%"
            >
              <el-option
                v-for="shop in shops"
                :key="shop.id"
                :label="shop.name"
                :value="shop.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="订单号" prop="order_number">
            <el-input
              v-model="formData.order_number"
              placeholder="请输入订单号"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="创收金额" prop="total_revenue">
            <el-input-number
              v-model="formData.total_revenue"
              :min="0"
              :precision="2"
              :step="0.01"
              style="width: 100%"
              placeholder="请输入创收金额"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="订单状态" prop="status">
            <el-select
              v-model="formData.status"
              placeholder="选择订单状态"
              style="width: 100%"
            >
              <el-option label="未做单" value="未做单" />
              <el-option label="已做单" value="已做单" />
              <el-option label="已关闭" value="已关闭" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="产品信息" prop="product_info">
        <el-input
          v-model="formData.product_info"
          type="textarea"
          :rows="3"
          placeholder="请输入产品信息，格式：创收金额+具体型号"
        />
      </el-form-item>
      
      <el-form-item label="创收备注">
        <el-input
          v-model="formData.revenue_notes"
          type="textarea"
          :rows="2"
          placeholder="请输入创收备注（可选）"
        />
      </el-form-item>
      
      <el-form-item label="产品图片">
        <div class="image-upload-container">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleImageChange"
            :before-upload="beforeImageUpload"
            accept="image/*"
            drag
          >
            <div v-if="!imagePreview" class="upload-placeholder">
              <i class="fas fa-cloud-upload-alt"></i>
              <div class="upload-text">
                <p>点击或拖拽图片到此处上传</p>
                <p class="upload-hint">支持 PNG、JPG、GIF 格式，最大 5MB</p>
              </div>
            </div>
            
            <div v-else class="image-preview">
              <img :src="imagePreview" alt="预览图片" />
              <div class="image-overlay">
                <el-button
                  type="danger"
                  :icon="Delete"
                  circle
                  size="small"
                  @click.stop="removeImage"
                />
              </div>
            </div>
          </el-upload>
          
          <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
            <el-progress :percentage="uploadProgress" />
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="isSubmitting"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { useRevenueStore } from '@/stores/revenue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: null
  },
  shops: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const revenueStore = useRevenueStore()

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const visible = ref(false)
const isSubmitting = ref(false)
const uploadProgress = ref(0)
const imageFile = ref(null)
const imagePreview = ref('')

// 表单数据
const formData = ref({
  shop_name: '',
  order_number: '',
  product_info: '',
  total_revenue: 0,
  revenue_notes: '',
  status: '未做单'
})

// 表单验证规则
const formRules = {
  shop_name: [
    { required: true, message: '请选择或输入店铺名称', trigger: 'blur' }
  ],
  order_number: [
    { required: true, message: '请输入订单号', trigger: 'blur' },
    { min: 1, max: 100, message: '订单号长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  product_info: [
    { required: true, message: '请输入产品信息', trigger: 'blur' }
  ],
  total_revenue: [
    { required: true, message: '请输入创收金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '创收金额不能小于0', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择订单状态', trigger: 'change' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.record)

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    initForm()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 初始化表单
const initForm = async () => {
  await nextTick()
  
  if (props.record) {
    // 编辑模式
    formData.value = {
      shop_name: props.record.shop_name || '',
      order_number: props.record.order_number || '',
      product_info: props.record.product_info || '',
      total_revenue: props.record.total_revenue || 0,
      revenue_notes: props.record.revenue_notes || '',
      status: props.record.status || '未做单'
    }
    
    // 设置图片预览
    if (props.record.image_url) {
      imagePreview.value = props.record.image_url
    }
  } else {
    // 新增模式
    formData.value = {
      shop_name: '',
      order_number: '',
      product_info: '',
      total_revenue: 0,
      revenue_notes: '',
      status: '未做单'
    }
    imagePreview.value = ''
    imageFile.value = null
  }
  
  // 清除表单验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 处理图片变化
const handleImageChange = (file) => {
  imageFile.value = file.raw
  
  // 创建预览
  const reader = new FileReader()
  reader.onload = (e) => {
    imagePreview.value = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isValidType = ['image/jpeg', 'image/png', 'image/gif'].includes(file.type)
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isValidType) {
    ElMessage.error('只能上传 JPG、PNG、GIF 格式的图片!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 移除图片
const removeImage = () => {
  imagePreview.value = ''
  imageFile.value = null
  uploadProgress.value = 0
  
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    isSubmitting.value = true
    
    let result
    if (isEdit.value) {
      result = await revenueStore.updateRecord(
        props.record.id,
        formData.value,
        imageFile.value
      )
    } else {
      result = await revenueStore.createRecord(
        formData.value,
        imageFile.value
      )
    }
    
    if (result.success) {
      ElMessage.success(result.message)
      emit('success')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.image-upload-container {
  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 180px;
    border: 2px dashed var(--border-base);
    border-radius: 8px;
    background-color: var(--border-extra-light);
    transition: var(--transition-base);
    
    &:hover {
      border-color: var(--primary-color);
      background-color: rgba(64, 158, 255, 0.05);
    }
    
    i {
      font-size: 48px;
      color: var(--text-placeholder);
      margin-bottom: 16px;
    }
    
    .upload-text {
      text-align: center;
      
      p {
        margin: 0;
        color: var(--text-regular);
        
        &.upload-hint {
          font-size: 12px;
          color: var(--text-placeholder);
          margin-top: 4px;
        }
      }
    }
  }
  
  .image-preview {
    position: relative;
    width: 100%;
    height: 180px;
    border-radius: 8px;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: var(--transition-base);
      
      &:hover {
        opacity: 1;
      }
    }
  }
  
  .upload-progress {
    margin-top: 12px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
