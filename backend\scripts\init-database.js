const mysql = require('mysql2/promise');
require('dotenv').config();

async function initDatabase() {
  let connection;
  
  try {
    // 连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD
    });

    console.log('✅ 连接到MySQL服务器成功');

    // 创建数据库（如果不存在）
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${process.env.DB_NAME}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`✅ 数据库 ${process.env.DB_NAME} 创建成功`);

    // 选择数据库
    await connection.execute(`USE \`${process.env.DB_NAME}\``);

    // 创建用户表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) UNIQUE,
        role VARCHAR(20) DEFAULT 'user',
        avatar VARCHAR(255),
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_email (email)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ 用户表创建成功');

    // 创建店铺表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS shops (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ 店铺表创建成功');

    // 创建创收记录表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS revenue_records (
        id INT PRIMARY KEY AUTO_INCREMENT,
        order_number VARCHAR(100) UNIQUE NOT NULL,
        shop_name VARCHAR(100) NOT NULL,
        product_info TEXT,
        amount DECIMAL(10,2) NOT NULL,
        status ENUM('pending', 'completed', 'cancelled', 'refunded') DEFAULT 'pending',
        order_date DATE NOT NULL,
        image_url VARCHAR(255),
        notes TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_order_number (order_number),
        INDEX idx_shop_name (shop_name),
        INDEX idx_status (status),
        INDEX idx_order_date (order_date),
        INDEX idx_created_by (created_by),
        INDEX idx_amount (amount)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ 创收记录表创建成功');

    // 插入默认管理员用户
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('123456', 12);
    
    await connection.execute(`
      INSERT IGNORE INTO users (username, password, role, email) 
      VALUES ('admin', ?, 'admin', '<EMAIL>')
    `, [hashedPassword]);
    console.log('✅ 默认管理员用户创建成功 (用户名: admin, 密码: 123456)');

    // 插入示例店铺数据
    const sampleShops = [
      ['淘宝店铺', '主要销售电子产品'],
      ['京东店铺', '主要销售家居用品'],
      ['拼多多店铺', '主要销售日用品'],
      ['抖音小店', '主要销售美妆产品']
    ];

    for (const [name, description] of sampleShops) {
      await connection.execute(`
        INSERT IGNORE INTO shops (name, description) VALUES (?, ?)
      `, [name, description]);
    }
    console.log('✅ 示例店铺数据插入成功');

    console.log('\n🎉 数据库初始化完成！');
    console.log('📊 数据库信息:');
    console.log(`   - 数据库名: ${process.env.DB_NAME}`);
    console.log(`   - 主机: ${process.env.DB_HOST}:${process.env.DB_PORT}`);
    console.log(`   - 用户: ${process.env.DB_USER}`);
    console.log('\n👤 默认管理员账户:');
    console.log('   - 用户名: admin');
    console.log('   - 密码: 123456');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = initDatabase;
