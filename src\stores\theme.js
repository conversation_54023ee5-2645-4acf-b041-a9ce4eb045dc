import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const theme = ref(localStorage.getItem('theme') || 'system')

  // 计算属性
  const isDark = computed(() => {
    if (theme.value === 'dark') return true
    if (theme.value === 'light') return false
    // system 模式下根据系统主题决定
    return window.matchMedia('(prefers-color-scheme: dark)').matches
  })

  // 设置主题
  const setTheme = (newTheme) => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    applyTheme()
  }

  // 切换主题
  const toggleTheme = () => {
    const themes = ['light', 'dark', 'system']
    const currentIndex = themes.indexOf(theme.value)
    const nextIndex = (currentIndex + 1) % themes.length
    setTheme(themes[nextIndex])
  }

  // 应用主题
  const applyTheme = () => {
    const html = document.documentElement
    
    if (isDark.value) {
      html.classList.add('dark')
      html.setAttribute('data-theme', 'dark')
    } else {
      html.classList.remove('dark')
      html.setAttribute('data-theme', 'light')
    }
  }

  // 初始化主题
  const initTheme = () => {
    applyTheme()
    
    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', () => {
      if (theme.value === 'system') {
        applyTheme()
      }
    })
  }

  // 获取主题图标
  const getThemeIcon = computed(() => {
    switch (theme.value) {
      case 'light':
        return 'fas fa-sun'
      case 'dark':
        return 'fas fa-moon'
      case 'system':
        return 'fas fa-desktop'
      default:
        return 'fas fa-adjust'
    }
  })

  // 获取主题名称
  const getThemeName = computed(() => {
    switch (theme.value) {
      case 'light':
        return '浅色模式'
      case 'dark':
        return '深色模式'
      case 'system':
        return '跟随系统'
      default:
        return '自动'
    }
  })

  return {
    // 状态
    theme: readonly(theme),
    
    // 计算属性
    isDark,
    getThemeIcon,
    getThemeName,
    
    // 方法
    setTheme,
    toggleTheme,
    initTheme,
    applyTheme
  }
})
