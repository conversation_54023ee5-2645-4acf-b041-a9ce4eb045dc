<template>
  <div class="revenue-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="fas fa-dollar-sign"></i>
          创收管理
        </h1>
        <p class="page-description">管理和跟踪所有创收记录</p>
      </div>
      
      <div class="header-actions">
        <el-button
          type="primary"
          :icon="Plus"
          @click="showCreateDialog"
        >
          新增记录
        </el-button>
        
        <el-button
          :icon="Download"
          @click="exportData"
          :loading="isExporting"
        >
          导出数据
        </el-button>
        
        <el-button
          :icon="TrendCharts"
          @click="showChartsDialog"
        >
          数据分析
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-card total-revenue">
            <div class="stat-icon">
              <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-content">
              <div class="stat-label">总创收</div>
              <div class="stat-value">{{ formatCurrency(statistics.totalRevenue) }}</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-card total-orders">
            <div class="stat-icon">
              <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stat-content">
              <div class="stat-label">订单数量</div>
              <div class="stat-value">{{ formatNumber(statistics.totalOrders) }}</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-card total-shops">
            <div class="stat-icon">
              <i class="fas fa-store"></i>
            </div>
            <div class="stat-content">
              <div class="stat-label">店铺数量</div>
              <div class="stat-value">{{ formatNumber(statistics.totalShops) }}</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-card avg-order">
            <div class="stat-icon">
              <i class="fas fa-chart-bar"></i>
            </div>
            <div class="stat-content">
              <div class="stat-label">平均订单价值</div>
              <div class="stat-value">{{ formatCurrency(statistics.avgOrderValue) }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar">
      <el-card>
        <el-row :gutter="16" align="middle">
          <el-col :xs="24" :sm="8" :md="6">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索订单号、店铺名称..."
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
            />
          </el-col>
          
          <el-col :xs="24" :sm="8" :md="4">
            <el-select
              v-model="selectedShop"
              placeholder="选择店铺"
              clearable
              @change="handleFilter"
            >
              <el-option
                v-for="shop in shops"
                :key="shop.id"
                :label="shop.name"
                :value="shop.name"
              />
            </el-select>
          </el-col>
          
          <el-col :xs="24" :sm="8" :md="4">
            <el-select
              v-model="selectedStatus"
              placeholder="选择状态"
              clearable
              @change="handleFilter"
            >
              <el-option label="未做单" value="未做单" />
              <el-option label="已做单" value="已做单" />
              <el-option label="已关闭" value="已关闭" />
            </el-select>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="6">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleFilter"
            />
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="4">
            <el-button
              type="primary"
              :icon="Refresh"
              @click="resetFilters"
            >
              重置
            </el-button>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="data-table">
      <el-card>
        <el-table
          v-loading="isLoading"
          :data="records"
          stripe
          @sort-change="handleSortChange"
        >
          <el-table-column
            prop="shop_name"
            label="店铺名称"
            min-width="120"
            sortable="custom"
          />
          
          <el-table-column
            prop="order_number"
            label="订单号"
            min-width="150"
            sortable="custom"
          />
          
          <el-table-column
            prop="product_info"
            label="产品信息"
            min-width="200"
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="total_revenue"
            label="创收金额"
            min-width="120"
            sortable="custom"
          >
            <template #default="{ row }">
              <span class="revenue-amount">{{ formatCurrency(row.total_revenue) }}</span>
            </template>
          </el-table-column>
          
          <el-table-column
            prop="status"
            label="状态"
            min-width="100"
          >
            <template #default="{ row }">
              <el-select
                :model-value="row.status"
                size="small"
                @change="(value) => updateStatus(row.id, value)"
              >
                <el-option label="未做单" value="未做单" />
                <el-option label="已做单" value="已做单" />
                <el-option label="已关闭" value="已关闭" />
              </el-select>
            </template>
          </el-table-column>
          
          <el-table-column
            prop="image_url"
            label="图片"
            width="80"
          >
            <template #default="{ row }">
              <el-image
                v-if="row.image_url"
                :src="row.image_url"
                :preview-src-list="[row.image_url]"
                class="table-image"
                fit="cover"
              />
              <span v-else class="no-image">无图片</span>
            </template>
          </el-table-column>
          
          <el-table-column
            prop="created_at"
            label="创建时间"
            min-width="160"
            sortable="custom"
          >
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column
            label="操作"
            width="120"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                :icon="Edit"
                @click="showEditDialog(row)"
              />
              <el-button
                type="danger"
                size="small"
                :icon="Delete"
                @click="handleDelete(row)"
              />
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 创建/编辑对话框 -->
    <RecordDialog
      v-model="dialogVisible"
      :record="currentRecord"
      :shops="shops"
      @success="handleDialogSuccess"
    />

    <!-- 图表分析对话框 -->
    <ChartsDialog
      v-model="chartsDialogVisible"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Download,
  TrendCharts,
  Search,
  Refresh,
  Edit,
  Delete
} from '@element-plus/icons-vue'

import { useRevenueStore } from '@/stores/revenue'
import { formatCurrency, formatNumber, formatDate, debounce } from '@/utils/format'
import RecordDialog from '@/components/RecordDialog.vue'
import ChartsDialog from '@/components/ChartsDialog.vue'

const revenueStore = useRevenueStore()

// 响应式数据
const dialogVisible = ref(false)
const chartsDialogVisible = ref(false)
const currentRecord = ref(null)
const searchKeyword = ref('')
const selectedShop = ref('')
const selectedStatus = ref('')
const dateRange = ref([])
const isExporting = ref(false)

// 计算属性
const records = computed(() => revenueStore.records)
const statistics = computed(() => revenueStore.statistics)
const shops = computed(() => revenueStore.shops)
const isLoading = computed(() => revenueStore.isLoading)
const pagination = computed(() => revenueStore.pagination)

// 防抖搜索
const handleSearch = debounce((value) => {
  revenueStore.setFilters({ search: value })
  loadData()
}, 300)

// 处理筛选
const handleFilter = () => {
  const filters = {
    shopName: selectedShop.value,
    status: selectedStatus.value
  }
  
  if (dateRange.value && dateRange.value.length === 2) {
    filters.startDate = dateRange.value[0]
    filters.endDate = dateRange.value[1]
  }
  
  revenueStore.setFilters(filters)
  loadData()
}

// 重置筛选
const resetFilters = () => {
  searchKeyword.value = ''
  selectedShop.value = ''
  selectedStatus.value = ''
  dateRange.value = []
  
  revenueStore.resetFilters()
  loadData()
}

// 显示创建对话框
const showCreateDialog = () => {
  currentRecord.value = null
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (record) => {
  currentRecord.value = { ...record }
  dialogVisible.value = true
}

// 显示图表对话框
const showChartsDialog = () => {
  chartsDialogVisible.value = true
}

// 处理对话框成功
const handleDialogSuccess = () => {
  dialogVisible.value = false
  loadData()
}

// 更新状态
const updateStatus = async (id, status) => {
  const result = await revenueStore.updateRecordStatus(id, status)
  if (result.success) {
    ElMessage.success(result.message)
  } else {
    ElMessage.error(result.message)
  }
}

// 处理删除
const handleDelete = async (record) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除订单号为 "${record.order_number}" 的记录吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const result = await revenueStore.deleteRecord(record.id)
    if (result.success) {
      ElMessage.success(result.message)
      loadData()
    } else {
      ElMessage.error(result.message)
    }
  } catch {
    // 用户取消删除
  }
}

// 处理排序
const handleSortChange = ({ prop, order }) => {
  // 实现排序逻辑
  console.log('排序:', prop, order)
}

// 处理分页大小变化
const handlePageSizeChange = (size) => {
  revenueStore.setPagination({ pageSize: size, current: 1 })
  loadData()
}

// 处理页码变化
const handlePageChange = (page) => {
  revenueStore.setPagination({ current: page })
  loadData()
}

// 导出数据
const exportData = async () => {
  isExporting.value = true
  try {
    // 实现导出逻辑
    ElMessage.success('导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    isExporting.value = false
  }
}

// 加载数据
const loadData = async () => {
  await Promise.all([
    revenueStore.fetchRecords(),
    revenueStore.fetchStatistics(),
    revenueStore.fetchShops()
  ])
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.revenue-management {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 28px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;

        i {
          color: var(--primary-color);
        }
      }

      .page-description {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
  }

  .statistics-cards {
    margin-bottom: 24px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 24px;
      background: var(--bg-color);
      border-radius: 12px;
      box-shadow: var(--box-shadow-light);
      transition: var(--transition-base);
      height: 100px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--box-shadow-dark);
      }

      .stat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 52px;
        height: 52px;
        border-radius: 12px;
        margin-right: 16px;

        i {
          font-size: 24px;
          color: white;
        }
      }

      .stat-content {
        flex: 1;

        .stat-label {
          font-size: 14px;
          color: var(--text-secondary);
          margin-bottom: 4px;
        }

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--text-primary);
        }
      }

      &.total-revenue .stat-icon {
        background: linear-gradient(135deg, #667eea, #764ba2);
      }

      &.total-orders .stat-icon {
        background: linear-gradient(135deg, #f093fb, #f5576c);
      }

      &.total-shops .stat-icon {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
      }

      &.avg-order .stat-icon {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
      }
    }
  }

  .filter-toolbar {
    margin-bottom: 24px;

    .el-card {
      border-radius: 12px;
      box-shadow: var(--box-shadow-light);
    }
  }

  .data-table {
    .el-card {
      border-radius: 12px;
      box-shadow: var(--box-shadow-light);
    }

    .table-image {
      width: 40px;
      height: 40px;
      border-radius: 6px;
      cursor: pointer;
    }

    .no-image {
      color: var(--text-placeholder);
      font-size: 12px;
    }

    .revenue-amount {
      font-weight: 600;
      color: var(--success-color);
    }

    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 24px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .revenue-management {
    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: flex-start;
      }
    }

    .statistics-cards {
      .stat-card {
        padding: 16px;
        height: auto;

        .stat-icon {
          width: 40px;
          height: 40px;
          margin-right: 12px;

          i {
            font-size: 20px;
          }
        }

        .stat-content .stat-value {
          font-size: 20px;
        }
      }
    }
  }
}
</style>
