const { sequelize } = require('../config/database');
const User = require('./User');
const Shop = require('./Shop');
const RevenueRecord = require('./RevenueRecord');

// 定义模型关联关系
User.hasMany(RevenueRecord, {
  foreignKey: 'createdBy',
  as: 'revenueRecords'
});

RevenueRecord.belongsTo(User, {
  foreignKey: 'createdBy',
  as: 'creator'
});

// 导出所有模型和数据库连接
module.exports = {
  sequelize,
  User,
  Shop,
  RevenueRecord
};
