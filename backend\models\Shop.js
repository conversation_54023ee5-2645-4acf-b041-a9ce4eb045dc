const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Shop = sequelize.define('Shop', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    validate: {
      len: [1, 100],
      notEmpty: true
    }
  },
  description: {
    type: DataTypes.TEXT
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active'
  }
}, {
  tableName: 'shops',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 类方法：获取活跃店铺
Shop.getActiveShops = async function() {
  return await this.findAll({
    where: { status: 'active' },
    order: [['name', 'ASC']]
  });
};

// 类方法：根据名称搜索店铺
Shop.searchByName = async function(searchTerm) {
  return await this.findAll({
    where: {
      name: {
        [sequelize.Sequelize.Op.like]: `%${searchTerm}%`
      }
    },
    order: [['name', 'ASC']]
  });
};

module.exports = Shop;
