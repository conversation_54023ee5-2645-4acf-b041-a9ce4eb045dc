<template>
  <el-dialog
    v-model="visible"
    title="数据分析"
    width="90%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-loading="isLoading" class="charts-container">
      <el-row :gutter="24">
        <!-- 创收趋势图 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <i class="fas fa-chart-line"></i>
                <span>创收趋势（最近30天）</span>
              </div>
            </template>
            <div class="chart-wrapper">
              <canvas ref="trendChartRef"></canvas>
            </div>
          </el-card>
        </el-col>
        
        <!-- 店铺创收分布 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <i class="fas fa-chart-pie"></i>
                <span>店铺创收分布</span>
              </div>
            </template>
            <div class="chart-wrapper">
              <canvas ref="shopChartRef"></canvas>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="24" style="margin-top: 24px;">
        <!-- 月度创收对比 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <i class="fas fa-chart-bar"></i>
                <span>月度创收对比</span>
              </div>
            </template>
            <div class="chart-wrapper">
              <canvas ref="monthlyChartRef"></canvas>
            </div>
          </el-card>
        </el-col>
        
        <!-- 订单状态分布 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <i class="fas fa-chart-donut"></i>
                <span>订单状态分布</span>
              </div>
            </template>
            <div class="chart-wrapper">
              <canvas ref="statusChartRef"></canvas>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 数据摘要 -->
      <el-row style="margin-top: 24px;">
        <el-col :span="24">
          <el-card class="summary-card">
            <template #header>
              <div class="chart-header">
                <i class="fas fa-info-circle"></i>
                <span>数据摘要</span>
              </div>
            </template>
            <div class="summary-content">
              <el-row :gutter="24">
                <el-col :xs="12" :sm="6">
                  <div class="summary-item">
                    <div class="summary-label">今日创收</div>
                    <div class="summary-value">{{ formatCurrency(summaryData.todayRevenue) }}</div>
                  </div>
                </el-col>
                <el-col :xs="12" :sm="6">
                  <div class="summary-item">
                    <div class="summary-label">本月创收</div>
                    <div class="summary-value">{{ formatCurrency(summaryData.monthRevenue) }}</div>
                  </div>
                </el-col>
                <el-col :xs="12" :sm="6">
                  <div class="summary-item">
                    <div class="summary-label">最高单日</div>
                    <div class="summary-value">{{ formatCurrency(summaryData.maxDayRevenue) }}</div>
                  </div>
                </el-col>
                <el-col :xs="12" :sm="6">
                  <div class="summary-item">
                    <div class="summary-label">增长率</div>
                    <div class="summary-value" :class="{ 'positive': summaryData.growthRate > 0, 'negative': summaryData.growthRate < 0 }">
                      {{ summaryData.growthRate > 0 ? '+' : '' }}{{ summaryData.growthRate.toFixed(1) }}%
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="refreshData" :loading="isLoading">
          <i class="fas fa-sync-alt"></i>
          刷新数据
        </el-button>
        <el-button @click="exportCharts">
          <i class="fas fa-download"></i>
          导出图表
        </el-button>
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Chart,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'
import { apiRequest } from '@/utils/api'
import { formatCurrency } from '@/utils/format'

// 注册Chart.js组件
Chart.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = ref(false)
const isLoading = ref(false)
const chartData = ref({})
const summaryData = ref({
  todayRevenue: 0,
  monthRevenue: 0,
  maxDayRevenue: 0,
  growthRate: 0
})

// 图表引用
const trendChartRef = ref()
const shopChartRef = ref()
const monthlyChartRef = ref()
const statusChartRef = ref()

// 图表实例
let trendChart = null
let shopChart = null
let monthlyChart = null
let statusChart = null

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    nextTick(() => {
      loadChartData()
    })
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    destroyCharts()
  }
})

// 加载图表数据
const loadChartData = async () => {
  isLoading.value = true
  try {
    const response = await apiRequest('/api/revenue-records/charts', {
      method: 'GET'
    })
    
    if (response.success) {
      chartData.value = response.data
      summaryData.value = response.data.summary || summaryData.value
      
      await nextTick()
      initCharts()
    } else {
      ElMessage.error('加载图表数据失败')
    }
  } catch (error) {
    console.error('加载图表数据失败:', error)
    ElMessage.error('加载图表数据失败')
  } finally {
    isLoading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  initTrendChart()
  initShopChart()
  initMonthlyChart()
  initStatusChart()
}

// 初始化趋势图
const initTrendChart = () => {
  if (!trendChartRef.value || !chartData.value.trend) return
  
  const ctx = trendChartRef.value.getContext('2d')
  
  trendChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: chartData.value.trend.map(item => item.date),
      datasets: [{
        label: '创收金额',
        data: chartData.value.trend.map(item => item.revenue),
        borderColor: '#409eff',
        backgroundColor: 'rgba(64, 158, 255, 0.1)',
        fill: true,
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        title: {
          display: false
        },
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return '¥' + value.toLocaleString()
            }
          }
        }
      }
    }
  })
}

// 初始化店铺分布图
const initShopChart = () => {
  if (!shopChartRef.value || !chartData.value.shops) return
  
  const ctx = shopChartRef.value.getContext('2d')
  
  const colors = [
    '#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399',
    '#ff7875', '#ffa940', '#fadb14', '#52c41a', '#13c2c2'
  ]
  
  shopChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: chartData.value.shops.map(item => item.shop_name),
      datasets: [{
        data: chartData.value.shops.map(item => item.revenue),
        backgroundColor: colors.slice(0, chartData.value.shops.length),
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom'
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const value = context.parsed
              const total = context.dataset.data.reduce((a, b) => a + b, 0)
              const percentage = ((value / total) * 100).toFixed(1)
              return `${context.label}: ¥${value.toLocaleString()} (${percentage}%)`
            }
          }
        }
      }
    }
  })
}

// 初始化月度对比图
const initMonthlyChart = () => {
  if (!monthlyChartRef.value || !chartData.value.monthly) return
  
  const ctx = monthlyChartRef.value.getContext('2d')
  
  monthlyChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: chartData.value.monthly.map(item => item.month),
      datasets: [{
        label: '创收金额',
        data: chartData.value.monthly.map(item => item.revenue),
        backgroundColor: 'rgba(64, 158, 255, 0.8)',
        borderColor: '#409eff',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return '¥' + value.toLocaleString()
            }
          }
        }
      }
    }
  })
}

// 初始化状态分布图
const initStatusChart = () => {
  if (!statusChartRef.value || !chartData.value.status) return
  
  const ctx = statusChartRef.value.getContext('2d')
  
  statusChart = new Chart(ctx, {
    type: 'pie',
    data: {
      labels: chartData.value.status.map(item => item.status),
      datasets: [{
        data: chartData.value.status.map(item => item.count),
        backgroundColor: ['#909399', '#67c23a', '#f56c6c'],
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    }
  })
}

// 销毁图表
const destroyCharts = () => {
  if (trendChart) {
    trendChart.destroy()
    trendChart = null
  }
  if (shopChart) {
    shopChart.destroy()
    shopChart = null
  }
  if (monthlyChart) {
    monthlyChart.destroy()
    monthlyChart = null
  }
  if (statusChart) {
    statusChart.destroy()
    statusChart = null
  }
}

// 刷新数据
const refreshData = () => {
  loadChartData()
}

// 导出图表
const exportCharts = () => {
  ElMessage.info('导出功能开发中...')
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.charts-container {
  min-height: 600px;

  .chart-card {
    height: 400px;

    .chart-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: var(--text-primary);

      i {
        color: var(--primary-color);
      }
    }

    .chart-wrapper {
      height: 320px;
      position: relative;
    }
  }

  .summary-card {
    .summary-content {
      .summary-item {
        text-align: center;
        padding: 16px;

        .summary-label {
          font-size: 14px;
          color: var(--text-secondary);
          margin-bottom: 8px;
        }

        .summary-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--text-primary);

          &.positive {
            color: var(--success-color);
          }

          &.negative {
            color: var(--danger-color);
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    i {
      margin-right: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .charts-container {
    .chart-card {
      height: 300px;
      margin-bottom: 16px;

      .chart-wrapper {
        height: 220px;
      }
    }

    .summary-card {
      .summary-content {
        .summary-item {
          padding: 12px;

          .summary-value {
            font-size: 20px;
          }
        }
      }
    }
  }
}
</style>
