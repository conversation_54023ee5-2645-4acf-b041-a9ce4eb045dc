const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { RevenueRecord, Shop } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const router = express.Router();

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024 // 5MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件 (jpeg, jpg, png, gif, webp)'));
    }
  }
});

// 获取创收记录列表
router.get('/', authenticateToken, [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须是1-100之间的整数'),
  query('search').optional().isString().withMessage('搜索关键词必须是字符串'),
  query('shopName').optional().isString().withMessage('店铺名称必须是字符串'),
  query('status').optional().isIn(['pending', 'completed', 'cancelled', 'refunded']).withMessage('状态值无效'),
  query('startDate').optional().isDate().withMessage('开始日期格式无效'),
  query('endDate').optional().isDate().withMessage('结束日期格式无效')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      pageSize = 10,
      search,
      shopName,
      status,
      startDate,
      endDate
    } = req.query;

    // 构建查询条件
    const whereClause = {};
    
    if (search) {
      whereClause[RevenueRecord.sequelize.Sequelize.Op.or] = [
        { orderNumber: { [RevenueRecord.sequelize.Sequelize.Op.like]: `%${search}%` } },
        { shopName: { [RevenueRecord.sequelize.Sequelize.Op.like]: `%${search}%` } },
        { productInfo: { [RevenueRecord.sequelize.Sequelize.Op.like]: `%${search}%` } },
        { notes: { [RevenueRecord.sequelize.Sequelize.Op.like]: `%${search}%` } }
      ];
    }
    
    if (shopName) {
      whereClause.shopName = shopName;
    }
    
    if (status) {
      whereClause.status = status;
    }
    
    if (startDate && endDate) {
      whereClause.orderDate = {
        [RevenueRecord.sequelize.Sequelize.Op.between]: [startDate, endDate]
      };
    } else if (startDate) {
      whereClause.orderDate = {
        [RevenueRecord.sequelize.Sequelize.Op.gte]: startDate
      };
    } else if (endDate) {
      whereClause.orderDate = {
        [RevenueRecord.sequelize.Sequelize.Op.lte]: endDate
      };
    }

    // 分页查询
    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    const limit = parseInt(pageSize);

    const { count, rows } = await RevenueRecord.findAndCountAll({
      where: whereClause,
      order: [['orderDate', 'DESC'], ['createdAt', 'DESC']],
      offset,
      limit
    });

    res.json({
      success: true,
      data: {
        records: rows,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total: count,
          totalPages: Math.ceil(count / parseInt(pageSize))
        }
      }
    });
  } catch (error) {
    console.error('获取创收记录错误:', error);
    res.status(500).json({
      success: false,
      message: '获取创收记录失败'
    });
  }
});

// 获取单个创收记录
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const record = await RevenueRecord.findByPk(id);
    if (!record) {
      return res.status(404).json({
        success: false,
        message: '创收记录不存在'
      });
    }

    res.json({
      success: true,
      data: record
    });
  } catch (error) {
    console.error('获取创收记录详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取创收记录详情失败'
    });
  }
});

// 创建创收记录
router.post('/', authenticateToken, [
  body('orderNumber').notEmpty().withMessage('订单号不能为空'),
  body('shopName').notEmpty().withMessage('店铺名称不能为空'),
  body('amount').isFloat({ min: 0 }).withMessage('金额必须是大于等于0的数字'),
  body('orderDate').isDate().withMessage('订单日期格式无效'),
  body('status').optional().isIn(['pending', 'completed', 'cancelled', 'refunded']).withMessage('状态值无效')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const recordData = {
      ...req.body,
      createdBy: req.user.id
    };

    const record = await RevenueRecord.create(recordData);

    res.status(201).json({
      success: true,
      message: '创收记录创建成功',
      data: record
    });
  } catch (error) {
    console.error('创建创收记录错误:', error);
    
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        success: false,
        message: '订单号已存在'
      });
    }
    
    res.status(500).json({
      success: false,
      message: '创建创收记录失败'
    });
  }
});

// 更新创收记录
router.put('/:id', authenticateToken, [
  body('orderNumber').optional().notEmpty().withMessage('订单号不能为空'),
  body('shopName').optional().notEmpty().withMessage('店铺名称不能为空'),
  body('amount').optional().isFloat({ min: 0 }).withMessage('金额必须是大于等于0的数字'),
  body('orderDate').optional().isDate().withMessage('订单日期格式无效'),
  body('status').optional().isIn(['pending', 'completed', 'cancelled', 'refunded']).withMessage('状态值无效')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    
    const record = await RevenueRecord.findByPk(id);
    if (!record) {
      return res.status(404).json({
        success: false,
        message: '创收记录不存在'
      });
    }

    await record.update(req.body);

    res.json({
      success: true,
      message: '创收记录更新成功',
      data: record
    });
  } catch (error) {
    console.error('更新创收记录错误:', error);
    
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        success: false,
        message: '订单号已存在'
      });
    }
    
    res.status(500).json({
      success: false,
      message: '更新创收记录失败'
    });
  }
});

// 删除创收记录
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const record = await RevenueRecord.findByPk(id);
    if (!record) {
      return res.status(404).json({
        success: false,
        message: '创收记录不存在'
      });
    }

    // 删除关联的图片文件
    if (record.imageUrl) {
      const imagePath = path.join(__dirname, '../uploads', path.basename(record.imageUrl));
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }

    await record.destroy();

    res.json({
      success: true,
      message: '创收记录删除成功'
    });
  } catch (error) {
    console.error('删除创收记录错误:', error);
    res.status(500).json({
      success: false,
      message: '删除创收记录失败'
    });
  }
});

// 批量删除创收记录
router.delete('/batch', authenticateToken, [
  body('ids').isArray({ min: 1 }).withMessage('必须提供至少一个ID'),
  body('ids.*').isInt({ min: 1 }).withMessage('ID必须是正整数')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { ids } = req.body;

    // 查找要删除的记录
    const records = await RevenueRecord.findAll({
      where: { id: ids }
    });

    // 删除关联的图片文件
    for (const record of records) {
      if (record.imageUrl) {
        const imagePath = path.join(__dirname, '../uploads', path.basename(record.imageUrl));
        if (fs.existsSync(imagePath)) {
          fs.unlinkSync(imagePath);
        }
      }
    }

    // 批量删除记录
    const deletedCount = await RevenueRecord.destroy({
      where: { id: ids }
    });

    res.json({
      success: true,
      message: `成功删除 ${deletedCount} 条记录`
    });
  } catch (error) {
    console.error('批量删除创收记录错误:', error);
    res.status(500).json({
      success: false,
      message: '批量删除创收记录失败'
    });
  }
});

// 上传图片
router.post('/upload', authenticateToken, upload.single('image'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的图片'
      });
    }

    const imageUrl = `/uploads/${req.file.filename}`;

    res.json({
      success: true,
      message: '图片上传成功',
      data: {
        url: imageUrl,
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size
      }
    });
  } catch (error) {
    console.error('图片上传错误:', error);
    res.status(500).json({
      success: false,
      message: '图片上传失败'
    });
  }
});

// 获取统计数据
router.get('/statistics/overview', authenticateToken, [
  query('shopName').optional().isString().withMessage('店铺名称必须是字符串'),
  query('status').optional().isIn(['pending', 'completed', 'cancelled', 'refunded']).withMessage('状态值无效'),
  query('startDate').optional().isDate().withMessage('开始日期格式无效'),
  query('endDate').optional().isDate().withMessage('结束日期格式无效')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const filters = {
      shopName: req.query.shopName,
      status: req.query.status,
      startDate: req.query.startDate,
      endDate: req.query.endDate
    };

    const statistics = await RevenueRecord.getStatistics(filters);

    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('获取统计数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败'
    });
  }
});

// 获取趋势数据
router.get('/statistics/trend', authenticateToken, [
  query('days').optional().isInt({ min: 1, max: 365 }).withMessage('天数必须是1-365之间的整数')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const days = parseInt(req.query.days) || 30;
    const trendData = await RevenueRecord.getTrendData(days);

    res.json({
      success: true,
      data: trendData
    });
  } catch (error) {
    console.error('获取趋势数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取趋势数据失败'
    });
  }
});

// 导出数据
router.get('/export', authenticateToken, [
  query('format').optional().isIn(['json', 'csv']).withMessage('导出格式只支持json或csv'),
  query('search').optional().isString().withMessage('搜索关键词必须是字符串'),
  query('shopName').optional().isString().withMessage('店铺名称必须是字符串'),
  query('status').optional().isIn(['pending', 'completed', 'cancelled', 'refunded']).withMessage('状态值无效'),
  query('startDate').optional().isDate().withMessage('开始日期格式无效'),
  query('endDate').optional().isDate().withMessage('结束日期格式无效')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const {
      format = 'json',
      search,
      shopName,
      status,
      startDate,
      endDate
    } = req.query;

    // 构建查询条件
    const whereClause = {};

    if (search) {
      whereClause[RevenueRecord.sequelize.Sequelize.Op.or] = [
        { orderNumber: { [RevenueRecord.sequelize.Sequelize.Op.like]: `%${search}%` } },
        { shopName: { [RevenueRecord.sequelize.Sequelize.Op.like]: `%${search}%` } },
        { productInfo: { [RevenueRecord.sequelize.Sequelize.Op.like]: `%${search}%` } },
        { notes: { [RevenueRecord.sequelize.Sequelize.Op.like]: `%${search}%` } }
      ];
    }

    if (shopName) whereClause.shopName = shopName;
    if (status) whereClause.status = status;

    if (startDate && endDate) {
      whereClause.orderDate = {
        [RevenueRecord.sequelize.Sequelize.Op.between]: [startDate, endDate]
      };
    } else if (startDate) {
      whereClause.orderDate = {
        [RevenueRecord.sequelize.Sequelize.Op.gte]: startDate
      };
    } else if (endDate) {
      whereClause.orderDate = {
        [RevenueRecord.sequelize.Sequelize.Op.lte]: endDate
      };
    }

    const records = await RevenueRecord.findAll({
      where: whereClause,
      order: [['orderDate', 'DESC'], ['createdAt', 'DESC']]
    });

    if (format === 'csv') {
      // 生成CSV格式
      const csvHeader = '订单号,店铺名称,产品信息,金额,状态,订单日期,备注,创建时间\n';
      const csvData = records.map(record => {
        return [
          record.orderNumber,
          record.shopName,
          record.productInfo || '',
          record.amount,
          record.status,
          record.orderDate,
          record.notes || '',
          record.createdAt
        ].map(field => `"${String(field).replace(/"/g, '""')}"`).join(',');
      }).join('\n');

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="revenue_records_${new Date().toISOString().split('T')[0]}.csv"`);
      res.send('\ufeff' + csvHeader + csvData); // 添加BOM以支持中文
    } else {
      // JSON格式
      res.json({
        success: true,
        data: records,
        exportTime: new Date().toISOString(),
        totalCount: records.length
      });
    }
  } catch (error) {
    console.error('导出数据错误:', error);
    res.status(500).json({
      success: false,
      message: '导出数据失败'
    });
  }
});

module.exports = router;
