{"name": "revenue-management-vue", "version": "1.0.0", "description": "Vue.js版本的创收管理系统", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.5.0", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^2.30.0", "viewerjs": "^1.11.6", "element-plus": "^2.3.12", "@element-plus/icons-vue": "^2.1.0", "pinyin-pro": "^3.15.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9", "sass": "^1.66.1", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2"}, "keywords": ["vue", "revenue", "management", "system"], "author": "Your Name", "license": "MIT"}