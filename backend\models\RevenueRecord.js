const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const RevenueRecord = sequelize.define('RevenueRecord', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderNumber: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    field: 'order_number',
    validate: {
      len: [1, 100],
      notEmpty: true
    }
  },
  shopName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'shop_name',
    validate: {
      len: [1, 100],
      notEmpty: true
    }
  },
  productInfo: {
    type: DataTypes.TEXT,
    field: 'product_info'
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0,
      isDecimal: true
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'completed', 'cancelled', 'refunded'),
    defaultValue: 'pending'
  },
  orderDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    field: 'order_date'
  },
  imageUrl: {
    type: DataTypes.STRING(255),
    field: 'image_url'
  },
  notes: {
    type: DataTypes.TEXT
  },
  createdBy: {
    type: DataTypes.INTEGER,
    field: 'created_by',
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'revenue_records',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    { fields: ['order_number'] },
    { fields: ['shop_name'] },
    { fields: ['status'] },
    { fields: ['order_date'] },
    { fields: ['amount'] }
  ]
});

// 类方法：搜索记录
RevenueRecord.searchRecords = async function(searchTerm, filters = {}) {
  const whereClause = {};
  
  // 搜索条件
  if (searchTerm) {
    whereClause[sequelize.Sequelize.Op.or] = [
      { orderNumber: { [sequelize.Sequelize.Op.like]: `%${searchTerm}%` } },
      { shopName: { [sequelize.Sequelize.Op.like]: `%${searchTerm}%` } },
      { productInfo: { [sequelize.Sequelize.Op.like]: `%${searchTerm}%` } },
      { notes: { [sequelize.Sequelize.Op.like]: `%${searchTerm}%` } }
    ];
  }
  
  // 筛选条件
  if (filters.shopName) {
    whereClause.shopName = filters.shopName;
  }
  
  if (filters.status) {
    whereClause.status = filters.status;
  }
  
  if (filters.startDate && filters.endDate) {
    whereClause.orderDate = {
      [sequelize.Sequelize.Op.between]: [filters.startDate, filters.endDate]
    };
  } else if (filters.startDate) {
    whereClause.orderDate = {
      [sequelize.Sequelize.Op.gte]: filters.startDate
    };
  } else if (filters.endDate) {
    whereClause.orderDate = {
      [sequelize.Sequelize.Op.lte]: filters.endDate
    };
  }
  
  return await this.findAll({
    where: whereClause,
    order: [['orderDate', 'DESC'], ['createdAt', 'DESC']]
  });
};

// 类方法：获取统计数据
RevenueRecord.getStatistics = async function(filters = {}) {
  const whereClause = {};
  
  // 应用筛选条件
  if (filters.shopName) {
    whereClause.shopName = filters.shopName;
  }
  
  if (filters.status) {
    whereClause.status = filters.status;
  }
  
  if (filters.startDate && filters.endDate) {
    whereClause.orderDate = {
      [sequelize.Sequelize.Op.between]: [filters.startDate, filters.endDate]
    };
  }
  
  const [totalStats, shopStats, statusStats] = await Promise.all([
    // 总体统计
    this.findOne({
      where: whereClause,
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalOrders'],
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalRevenue'],
        [sequelize.fn('AVG', sequelize.col('amount')), 'avgOrderValue'],
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('shop_name'))), 'totalShops']
      ],
      raw: true
    }),
    
    // 按店铺统计
    this.findAll({
      where: whereClause,
      attributes: [
        'shopName',
        [sequelize.fn('COUNT', sequelize.col('id')), 'orderCount'],
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount']
      ],
      group: ['shopName'],
      order: [[sequelize.fn('SUM', sequelize.col('amount')), 'DESC']],
      raw: true
    }),
    
    // 按状态统计
    this.findAll({
      where: whereClause,
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('amount')), 'amount']
      ],
      group: ['status'],
      raw: true
    })
  ]);
  
  return {
    total: {
      totalOrders: parseInt(totalStats?.totalOrders || 0),
      totalRevenue: parseFloat(totalStats?.totalRevenue || 0),
      avgOrderValue: parseFloat(totalStats?.avgOrderValue || 0),
      totalShops: parseInt(totalStats?.totalShops || 0)
    },
    byShop: shopStats.map(shop => ({
      shopName: shop.shopName,
      orderCount: parseInt(shop.orderCount),
      totalAmount: parseFloat(shop.totalAmount)
    })),
    byStatus: statusStats.map(status => ({
      status: status.status,
      count: parseInt(status.count),
      amount: parseFloat(status.amount)
    }))
  };
};

// 类方法：获取趋势数据
RevenueRecord.getTrendData = async function(days = 30) {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - days);
  
  return await this.findAll({
    where: {
      orderDate: {
        [sequelize.Sequelize.Op.between]: [startDate, endDate]
      }
    },
    attributes: [
      'orderDate',
      [sequelize.fn('COUNT', sequelize.col('id')), 'orderCount'],
      [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount']
    ],
    group: ['orderDate'],
    order: [['orderDate', 'ASC']],
    raw: true
  });
};

module.exports = RevenueRecord;
