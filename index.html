<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>创收管理系统 - Vue版</title>
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* 加载动画 */
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      transition: opacity 0.5s ease;
    }
    
    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid #ffffff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      color: white;
      margin-top: 20px;
      font-size: 18px;
      font-weight: 500;
    }
    
    /* 隐藏加载动画 */
    .loading-container.hidden {
      opacity: 0;
      pointer-events: none;
    }
  </style>
</head>
<body>
  <!-- 加载动画 -->
  <div id="loading" class="loading-container">
    <div class="text-center">
      <div class="loading-spinner"></div>
      <div class="loading-text">创收管理系统加载中...</div>
    </div>
  </div>
  
  <!-- Vue应用挂载点 -->
  <div id="app"></div>
  
  <script type="module" src="/src/main.js"></script>
  
  <script>
    // 页面加载完成后隐藏加载动画
    window.addEventListener('load', () => {
      setTimeout(() => {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.classList.add('hidden');
          setTimeout(() => {
            loading.style.display = 'none';
          }, 500);
        }
      }, 1000);
    });
  </script>
</body>
</html>
